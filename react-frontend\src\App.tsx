import { useState, useEffect, useCallback } from 'react';
import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { useChat } from '@/hooks/useChat';
import { ModalState } from '@/types';

// Layout components
import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import ChatContainer from '@/components/chat/ChatContainer';
import PreLoginUI from '@/components/auth/PreLoginUI';

// Modal components
import LoginModal from '@/components/modals/LoginModal';
import RegisterModal from '@/components/modals/RegisterModal';
import SettingsModal from '@/components/modals/SettingsModal';
import VoiceModal from '@/components/modals/VoiceModal';
import EscalationModal from '@/components/modals/EscalationModal';
import ArchivedChatsModal from '@/components/modals/ArchivedChatsModal';
import TwoFAModal from '@/components/modals/TwoFAModal';
// import FloatingActionButton from '@/components/ui/FloatingActionButton';
import CommandPalette from '@/components/ui/CommandPalette';
import { Upload } from 'lucide-react';
import ChatSearchModal from '@/components/modals/ChatSearchModal';
import { escalationAPI } from '@/utils/api';
import EscalationList from './components/chat/EscalationList';

function App() {
  const { theme, changeTheme } = useTheme();
  // const { user, isLoading, isLoggedIn, login, register, logout, updateUser, verify2FA, pendingLogin } = useAuth();
  // const chatHook = useChat();

  // Simple test state
  const isLoading = false;
  const isLoggedIn = false;



  const [modals, setModals] = useState<ModalState>({
    login: false,
    register: false,
    settings: false,
    voice: false,
    filePreview: false,
    escalation: false,
    archivedChats: false,
    twoFA: false,
  });

  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
    return localStorage.getItem('sidebarCollapsed') === 'true';
  });

  // Command Palette state
  const [isPaletteOpen, setPaletteOpen] = useState(false);

  // Chat Search Modal state
  const [isChatSearchOpen, setChatSearchOpen] = useState(false);

  // Keyboard shortcut for Command Palette (Cmd+K / Ctrl+K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === 'k') {
        e.preventDefault();
        setPaletteOpen((open) => !open);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Keyboard shortcut for Chat Search (Ctrl+/)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === '/') {
        e.preventDefault();
        setChatSearchOpen(true);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Command Palette actions
  const paletteActions = [
    {
      label: 'New Chat',
      onSelect: chatHook.createNewSession,
      icon: <Upload className="w-4 h-4 text-blue-500" />,
    },
    {
      label: 'Settings',
      onSelect: () => openModal('settings'),
    },
    {
      label: 'Logout',
      onSelect: logout,
    },
    // Add more actions as needed
  ];

  const openModal = useCallback((modalName: keyof ModalState) => {
    console.log('Opening modal:', modalName);
    setModals(prev => ({ ...prev, [modalName]: true }));
  }, []);

  const closeModal = useCallback((modalName: keyof ModalState) => {
    console.log('Closing modal:', modalName);
    setModals(prev => ({ ...prev, [modalName]: false }));
  }, []);

  const toggleSidebar = () => {
    const newCollapsed = !sidebarCollapsed;
    setSidebarCollapsed(newCollapsed);
    localStorage.setItem('sidebarCollapsed', newCollapsed.toString());
  };

  const [showHRTeam, setShowHRTeam] = useState(false);

  useEffect(() => {
    if (pendingLogin && !modals.twoFA) {
      closeModal('login');
      openModal('twoFA');
    }
  }, [pendingLogin, modals.twoFA, openModal, closeModal]);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="h-screen w-screen overflow-hidden">
      {!isLoggedIn ? (
        <PreLoginUI
          onLogin={() => console.log('Login clicked')}
          onRegister={() => console.log('Register clicked')}
          onSendMessage={(message) => console.log('Message:', message)}
        />
      ) : (
        <div className="flex h-full w-full bg-gray-50 dark:bg-gray-900 relative">
          {/* Mobile Sidebar Overlay */}
          {!sidebarCollapsed && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
              onClick={toggleSidebar}
            />
          )}

          {/* Sidebar */}
          <div className={`${
            sidebarCollapsed
              ? 'hidden md:block'
              : 'fixed md:relative z-50 md:z-auto'
          } flex-shrink-0`}>
            <Sidebar
              isCollapsed={sidebarCollapsed}
              onToggle={toggleSidebar}
              chatSessions={chatHook.chatSessions}
              currentSessionId={chatHook.currentSessionId}
              onNewChat={chatHook.createNewSession}
              onLoadSession={chatHook.loadSession}
              onDeleteSession={chatHook.deleteSession}
              onArchiveSession={chatHook.archiveSession}
              onRenameSession={chatHook.renameSession}
              onDownloadSession={chatHook.downloadSession}
              onOpenSearch={() => setChatSearchOpen(true)}
              onShowHRTeam={() => setShowHRTeam(true)}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col min-w-0">
            {/* Header */}
            <Header
              onOpenSettings={() => openModal('settings')}
              onLogout={logout}
              user={user}
            />
            {/* Main Content Switch */}
            {showHRTeam ? (
              <EscalationList onBack={() => setShowHRTeam(false)} />
            ) : (
              <ChatContainer
                messages={chatHook.messages}
                isLoading={chatHook.isLoading}
                attachedFiles={chatHook.attachedFiles}
                onSendMessage={chatHook.sendMessage}
                onAddFile={chatHook.addFileAttachment}
                onRemoveFile={chatHook.removeFileAttachment}
                onOpenVoice={() => openModal('voice')}
                onOpenEscalation={() => openModal('escalation')}
                user={user}
                onUpdateUser={updateUser}
                onClarificationRequest={chatHook.requestClarification}
              />
            )}
          </div>

          {/* Floating Action Button (FAB) for new chat/upload - Only show on mobile */}
        </div>
      )}

      {/* Modals */}
      {modals.login && (
          <LoginModal
            onClose={() => closeModal('login')}
            onLogin={login}
            onSwitchToRegister={() => {
              closeModal('login');
              openModal('register');
            }}
            onOpen2FA={() => {
              closeModal('login');
              openModal('twoFA');
            }}
          />
      )}

      {modals.register && (
          <RegisterModal
            onClose={() => closeModal('register')}
            onRegister={register}
            onSwitchToLogin={() => {
              closeModal('register');
              openModal('login');
            }}
          />
      )}

      {modals.settings && (
          <SettingsModal
            onClose={() => closeModal('settings')}
            theme={theme}
            onThemeChange={changeTheme}
            user={user}
            onUpdateUser={updateUser}
            onLogout={logout}
            onClearAllChats={chatHook.clearAllChats}
            onOpenArchivedChats={() => {
              closeModal('settings');
              openModal('archivedChats');
            }}
          />
      )}

      {modals.voice && (
          <VoiceModal
            onClose={() => closeModal('voice')}
            onSubmitTranscript={(transcript) => {
              chatHook.sendMessage(transcript);
              closeModal('voice');
            }}
          />
      )}

      {modals.escalation && (
          <EscalationModal
            onClose={() => closeModal('escalation')}
            user={user}
            onSubmit={async (formData) => {
              await escalationAPI.submit(formData);
              // Do not close the modal here, let the confirmation popup handle it
            }}
          />
      )}

      {modals.archivedChats && (
          <ArchivedChatsModal
            onClose={() => closeModal('archivedChats')}
            archivedSessions={chatHook.archivedSessions}
            onRestoreSession={(sessionId) => {
              // TODO: Implement restore functionality
              console.log('Restore session:', sessionId);
            }}
          />
      )}

      {modals.twoFA && (
          <TwoFAModal
            onClose={() => closeModal('twoFA')}
            email={pendingLogin?.email || undefined}
            onVerify={async (code) => {
              const result = await verify2FA(code);
              if (result.success) {
                closeModal('twoFA');
              }
              return result;
            }}
          />

        )}
      <ChatSearchModal
        isOpen={isChatSearchOpen}
        onClose={() => setChatSearchOpen(false)}
        chatSessions={chatHook.chatSessions}
        onSelectChat={(sessionId) => {
          chatHook.loadSession(sessionId);
          setChatSearchOpen(false);
        }}
      />
    </div>
  );
}

export default App;
