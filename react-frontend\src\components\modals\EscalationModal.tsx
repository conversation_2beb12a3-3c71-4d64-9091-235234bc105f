import React, { useState } from 'react';
import { User, EscalationForm } from '@/types';
import { Button } from '@/components/ui/button';
import { Upload, FileText, X } from 'lucide-react';
import { FileAttachment } from '@/types';

interface EscalationModalProps {
  onClose: () => void;
  onSubmit: (formData: EscalationForm) => void;
  user: User | null;
}

const EscalationModal: React.FC<EscalationModalProps> = ({ onClose, onSubmit, user }) => {
  const [formData, setFormData] = useState<EscalationForm>({
    hrPerson: '',
    issueType: 'policy',
    issueDescription: '',
    priority: 'medium',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<FileAttachment[]>([]);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);

  const hrPersons = [
    { value: 'Vijay Kumar Kodam', label: 'Vijay Kumar Kodam - HR Manager', email: '<EMAIL>' },
    { value: 'Suman', label: 'Suman - Finance Departement', email: '<EMAIL>' },
    { value: 'Chandra Shekar', label: 'Chandra Shekar - Benefits Coordinator', email: '<EMAIL>' },
    { value: 'Lavanya', label: 'Lavanya - Employee Relations', email: '<EMAIL>' },
  ];

  // Mapping from issueType to default HR representative value
  const issueTypeToHR: Record<string, string> = {
    policy: 'sarah.johnson',
    workplace: 'david.kim',
    harassment: 'david.kim',
    conflict: 'david.kim',
    compensation: 'mike.chen',
    benefits: 'lisa.rodriguez',
    leave: 'lisa.rodriguez',
    promotion: 'sarah.johnson',
    safety: 'david.kim',
    transfer: 'sarah.johnson',
    exit: 'sarah.johnson',
    documents: 'mike.chen',
    other: 'sarah.johnson',
  };
  const [hrManuallySelected, setHrManuallySelected] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => {
      // If issueType changes, auto-select HR unless user has manually selected
      if (name === 'issueType') {
        const autoHR = issueTypeToHR[value as keyof typeof issueTypeToHR] || '';
        return {
          ...prev,
          issueType: value as EscalationForm['issueType'],
          hrPerson: hrManuallySelected ? prev.hrPerson : autoHR,
        };
      }
      if (name === 'hrPerson') {
        setHrManuallySelected(true);
      }
      return { ...prev, [name]: value };
    });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const newFiles: FileAttachment[] = files.map((file) => ({
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      name: file.name,
      size: file.size,
      type: file.type,
      file: file,
    }));
    setUploadedFiles((prev) => [...prev, ...newFiles]);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const handleRemoveFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.hrPerson || !formData.issueDescription.trim() || (!isAnonymous && (!user || !formData.priority))) {
      return;
    }
    setIsSubmitting(true);
    try {
      const selectedHR = hrPersons.find(person => person.value === formData.hrPerson);
      const hrEmail = selectedHR ? selectedHR.email : undefined;
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      // Prepare payload
      const payload = {
        user_id: user?.id ? String(user.id) : '',
        issue_type: formData.issueType,
        message: formData.issueDescription,
        timestamp: new Date().toISOString(),
        priority: formData.priority,
        user: isAnonymous ? null : user,
        hrEmail,
      };
      if (uploadedFiles.length > 0) {
        // Use FormData for file upload
        const formDataObj = new FormData();
        Object.entries(payload).forEach(([key, value]) => {
          if (typeof value === 'object' && value !== null) {
            formDataObj.append(key, JSON.stringify(value));
          } else if (value !== undefined) {
            formDataObj.append(key, value);
          }
        });
        uploadedFiles.forEach(fileObj => {
          formDataObj.append('files', fileObj.file, fileObj.name);
        });
        await fetch('http://localhost:5052/api/escalations', {
          method: 'POST',
          body: formDataObj,
          credentials: 'include',
          headers: localStorage.getItem('token') ? { Authorization: `Bearer ${localStorage.getItem('token')}` } : undefined,
        }).then(res => {
          if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
          return res.json();
        });
      } else {
        // Fallback to JSON if no files
        await onSubmit(payload);
      }
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
      setFormData({
        hrPerson: '',
        issueType: '',
        issueDescription: '',
        priority: '',
      });
      setUploadedFiles([]);
    } catch (error) {
      setShowError(true);
      setTimeout(() => setShowError(false), 2000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-20 backdrop-blur-sm"
      style={{ background: 'rgba(0,0,0,0.15)' }}
    >
      <div
        className="bg-white rounded-xl shadow-xl p-4 w-full"
        style={{ maxWidth: 480, width: '100%', maxHeight: '80vh', overflow: 'auto' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-2 border-b border-primary-border dark:border-dark-border">
          <h2 className="text-lg font-semibold text-primary-text dark:text-dark-text">
            Escalate Issue to HR
          </h2>
          <button onClick={onClose} className="icon-btn">
            <i className="fas fa-times text-primary-text-secondary dark:text-dark-text-secondary"></i>
          </button>
        </div>

        {/* Body */}
        <div className="p-2">
          <form onSubmit={handleSubmit} className="space-y-2">
            {/* Anonymous Toggle */}
            <div className="form-group mb-1 flex items-center">
              <input
                type="checkbox"
                id="anonymous-toggle"
                checked={isAnonymous}
                onChange={() => setIsAnonymous(!isAnonymous)}
                className="mr-2"
              />
              <label htmlFor="anonymous-toggle" className="form-label cursor-pointer">
                Raise Anonymously
              </label>
            </div>
            {/* User Details */}
            <div className="form-group mb-1">
              <label className="form-label">Your Details:</label>
              <div className="p-3 bg-primary-secondary dark:bg-dark-secondary rounded border border-primary-border dark:border-dark-border text-sm">
                {isAnonymous ? (
                  <div className="text-primary-text-secondary dark:text-dark-text-secondary italic">Your details will be hidden from HR.</div>
                ) : (
                  <>
                    <div className="text-primary-text dark:text-dark-text font-medium">
                      {user?.fullName || 'User Name'}
                    </div>
                    <div className="text-primary-text-secondary dark:text-dark-text-secondary">
                      {user?.email || '<EMAIL>'}
                    </div>
                    {user?.employeeId && (
                      <div className="text-primary-text-secondary dark:text-dark-text-secondary">
                        Employee ID: {user.employeeId}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Issue Type - moved up */}
            <div className="form-group mb-1">
              <label htmlFor="issueType" className="form-label">
                Issue Type:
              </label>
              <select
                id="issueType"
                name="issueType"
                value={formData.issueType}
                onChange={handleInputChange}
                className="form-input"
                required
              >
                <option value="policy">Policy Related</option>
                <option value="workplace">Workplace Issue</option>
                <option value="harassment">Harassment or Misconduct</option>
                <option value="conflict">Conflict with Manager / Peer</option>
                <option value="compensation">Compensation / Salary Dispute</option>
                <option value="benefits">Benefits & Reimbursements</option>
                <option value="leave">Leave or Attendance Issue</option>
                <option value="promotion">Promotion / Appraisal Concern</option>
                <option value="safety">Health & Safety</option>
                <option value="transfer">Transfer / Relocation</option>
                <option value="exit">Exit / Resignation Issue</option>
                <option value="documents">Document or Payslip Error</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* HR Person - moved down */}
            <div className="form-group mb-1">
              <label htmlFor="hrPerson" className="form-label">
                Select HR Representative:
              </label>
              <select
                id="hrPerson"
                name="hrPerson"
                value={formData.hrPerson}
                onChange={handleInputChange}
                className="form-input"
                required
              >
                <option value="">Select HR Representative</option>
                {hrPersons.map((person) => (
                  <option key={person.value} value={person.value}>
                    {person.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Issue Description */}
            <div className="form-group mb-1 relative">
              <label htmlFor="issueDescription" className="form-label">
                Issue Description:
              </label>
              <textarea
                id="issueDescription"
                name="issueDescription"
                value={formData.issueDescription}
                onChange={handleInputChange}
                rows={3}
                className="form-input resize-none pr-12"
                placeholder="Describe your issue..."
                required
              />
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.txt,.md,.csv,.xls,.xlsx,.ppt,.pptx,.html,.htm,.json,.xml,.rtf,.odt,.ods,.odp,.png,.jpg,.jpeg,.gif,.bmp,.tiff,.webp"
                onChange={handleFileUpload}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => fileInputRef.current?.click()}
                className="absolute bottom-2 right-2 z-10 shadow-md focus:ring-2 focus:ring-primary w-8 h-8 p-0 flex items-center justify-center"
                title="Upload document"
              >
                <Upload className="w-4 h-4" />
              </Button>
            </div>
            {uploadedFiles.length > 0 && (
              <div className="flex flex-col gap-2 mt-2">
                {uploadedFiles.map((file) => (
                  <div key={file.id} className="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded px-3 py-1 text-sm">
                    <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                    <span className="font-medium text-gray-900 dark:text-gray-100 truncate max-w-xs">{file.name}</span>
                    <span className="text-gray-500 dark:text-gray-400 text-xs">({(file.size/1024).toFixed(1)} KB)</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveFile(file.id)}
                      className="ml-2 h-5 w-5 rounded hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-400 transition-colors flex items-center justify-center"
                      title="Remove file"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Priority */}
            <div className="form-group mb-1">
              <label htmlFor="priority" className="form-label">
                Priority Level:
              </label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                className="form-input"
                required
              >
                <option value="low">Low – Within 7 Working Days</option>
                <option value="medium">Medium – Within 3–5 Working Days</option>
                <option value="high">High – Within 48 Hours</option>
                <option value="urgent">Urgent – Same Day</option>
              </select>
            </div>

            {/* Form Actions */}
            <div className="flex justify-center space-x-2 pt-2">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isSubmitting || !formData.hrPerson || !formData.issueDescription.trim()}
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-2">
                    <div className="loading-spinner"></div>
                    <span>Submitting...</span>
                  </div>
                ) : (
                  'Submit Escalation'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
      {showConfirmation && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="text-green-600 text-lg font-semibold mb-2">Escalation submitted successfully! HR has been notified.</div>
            <button className="btn-primary mt-2" onClick={() => setShowConfirmation(false)}>OK</button>
          </div>
        </div>
      )}
      {showSuccess && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="text-green-600 text-lg font-semibold mb-2">Escalation submitted successfully!</div>
            <button className="btn-primary mt-2" onClick={() => setShowSuccess(false)}>OK</button>
          </div>
        </div>
      )}
      {showError && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="text-red-600 text-lg font-semibold mb-2">Failed to submit escalation.</div>
            <button className="btn-primary mt-2" onClick={() => setShowError(false)}>OK</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EscalationModal;
